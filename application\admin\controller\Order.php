<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use Exception;
use think\Db;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use translate\BaiduTranslate;
use translate\Translate;

/**
 * 订单管理
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{

    /**
     * Order模型对象
     * @var \app\admin\model\Order
     */
    protected $model = null;

    /**
     * 回退到单独翻译的方法
     * 当批量翻译失败时使用
     * @param array $wljson 物流JSON数据
     * @param object $tranclate 翻译对象
     */
    protected function fallbackTranslation(&$wljson, $tranclate)
    {
        try {
            if (isset($wljson['data'][0]['details'])) {
                foreach ($wljson['data'][0]['details'] as $k => $v) {
                    try {
                        if (!empty($v['track_location']) && is_string($v['track_location'])) {
                            try {
                                // 记录翻译请求
                                \think\Log::write('回退翻译请求 track_location: ' . (is_array($v['track_location']) ? json_encode($v['track_location']) : $v['track_location']), 'debug');

                                $fyh = $tranclate->translate($v['track_location'], 'auto', 'zh');

                                // 记录翻译结果
                                \think\Log::write('回退翻译结果 track_location: ' . (is_array($fyh) ? json_encode($fyh) : $fyh), 'debug');

                                // 只有当翻译结果不为空且与原文不同时才更新
                                if (!empty(trim($fyh)) && $fyh != $v['track_location']) {
                                    $wljson['data'][0]['details'][$k]['track_location'] = $fyh;
                                }

                                // 暂停一下，避免请求过于频繁
                                usleep(100000); // 暂停0.1秒
                            } catch (\Exception $e) {
                                \think\Log::write('回退翻译 track_location 失败: ' . $e->getMessage(), 'error');
                            }
                        }

                        if (!empty($v['track_description_en']) && is_string($v['track_description_en'])) {
                            try {
                                // 记录翻译请求
                                \think\Log::write('回退翻译请求 track_description_en: ' . (is_array($v['track_description_en']) ? json_encode($v['track_description_en']) : $v['track_description_en']), 'debug');

                                $fyh2 = $tranclate->translate($v['track_description_en'], 'auto', 'zh');

                                // 记录翻译结果
                                \think\Log::write('回退翻译结果 track_description_en: ' . (is_array($fyh2) ? json_encode($fyh2) : $fyh2), 'debug');

                                // 只有当翻译结果不为空且与原文不同时才更新
                                if (!empty(trim($fyh2)) && $fyh2 != $v['track_description_en']) {
                                    $wljson['data'][0]['details'][$k]['track_description_en'] = $fyh2;
                                }

                                // 暂停一下，避免请求过于频繁
                                usleep(100000); // 暂停0.1秒
                            } catch (\Exception $e) {
                                \think\Log::write('回退翻译 track_description_en 失败: ' . $e->getMessage(), 'error');
                            }
                        }

                        if (!empty($v['track_description']) && is_string($v['track_description'])) {
                            try {
                                // 记录翻译请求
                                \think\Log::write('回退翻译请求 track_description: ' . (is_array($v['track_description']) ? json_encode($v['track_description']) : $v['track_description']), 'debug');

                                $fyh3 = $tranclate->translate($v['track_description'], 'auto', 'zh');

                                // 记录翻译结果
                                \think\Log::write('回退翻译结果 track_description: ' . (is_array($fyh3) ? json_encode($fyh3) : $fyh3), 'debug');

                                // 只有当翻译结果不为空且与原文不同时才更新
                                if (!empty(trim($fyh3)) && $fyh3 != $v['track_description']) {
                                    $wljson['data'][0]['details'][$k]['track_description'] = $fyh3;
                                }

                                // 暂停一下，避免请求过于频繁
                                usleep(100000); // 暂停0.1秒
                            } catch (\Exception $e) {
                                \think\Log::write('回退翻译 track_description 失败: ' . $e->getMessage(), 'error');
                            }
                        }
                    } catch (\Exception $e) {
                        \think\Log::write('回退翻译单条记录失败: ' . $e->getMessage(), 'error');
                        continue; // 继续处理下一条记录
                    }
                }
            }
        } catch (\Exception $e) {
            \think\Log::write('回退翻译整体失败: ' . $e->getMessage(), 'error');
        }
    }

    protected $searchFields = 'id,names,ddbh';
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Order;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("ifjjlistList", $this->model->getIfjjlistList());
        $this->view->assign("ifdblistList", $this->model->getIfdblistList());
        $this->view->assign("ifbfList", $this->model->getIfbfList());
        $auth = $this->auth->getGroupIds();
        $this->gid = $auth[0];
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 编辑联系方式和收货地址
     */
    public function editaddress($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");

            // 记录提交的参数到日志文件，方便调试
            \think\Log::write('编辑地址表单提交参数: ' . json_encode($params), 'debug');

            if ($params) {
                Db::startTrans();
                try {
                    // 确保所有字段都有值，即使是空字符串
                    $data = [
                        'id' => $params['id'], // 确保包含ID字段
                        'khname' => array_key_exists('khname', $params) ? $params['khname'] : '',
                        'khaddr1' => array_key_exists('khaddr1', $params) ? $params['khaddr1'] : '',
                        'khaddr2' => array_key_exists('khaddr2', $params) ? $params['khaddr2'] : '',
                        'city' => array_key_exists('city', $params) ? $params['city'] : '',
                        'state' => array_key_exists('state', $params) ? $params['state'] : '',
                        'country' => array_key_exists('country', $params) ? $params['country'] : '',
                        'zipcode' => array_key_exists('zipcode', $params) ? $params['zipcode'] : ''
                    ];

                    // 记录处理后的数据到日志文件
                    \think\Log::write('处理后的数据: ' . json_encode($data), 'debug');

                    // 使用直接的数据库更新，确保空值也能被保存
                    // 使用原生SQL语句进行更新，确保空值也能被正确保存
                    $sql = "UPDATE fa_order SET
                        khname = :khname,
                        khaddr1 = :khaddr1,
                        khaddr2 = :khaddr2,
                        city = :city,
                        state = :state,
                        country = :country,
                        zipcode = :zipcode
                        WHERE id = :id";

                    $result = Db::execute($sql, [
                        'khname' => $data['khname'],
                        'khaddr1' => $data['khaddr1'],
                        'khaddr2' => $data['khaddr2'],
                        'city' => $data['city'],
                        'state' => $data['state'],
                        'country' => $data['country'],
                        'zipcode' => $data['zipcode'],
                        'id' => $data['id']
                    ]);

                    // 记录SQL执行结果到日志
                    \think\Log::write('SQL执行结果: ' . $result, 'debug');

                    // 提交事务
                    Db::commit();

                    // 即使没有行被更新，也认为是成功的，因为可能用户没有修改任何内容
                    // 重新获取更新后的数据
                    $updatedRow = $this->model->get($params['id']);

                    // 记录更新后的数据到日志
                    \think\Log::write('更新后的数据: ' . json_encode($updatedRow), 'debug');

                    // 返回成功结果，包含更新后的数据
                    return json([
                        'code' => 1,
                        'msg' => '保存成功',
                        'message' => '保存成功',
                        'data' => [
                            'row' => $updatedRow->toArray(),
                            'result' => true
                        ]
                    ]);
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
            } else {
                $this->error(__('Parameter %s can not be empty', ''));
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 打印标签
     */
    public function printlabel($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 检查订单状态和物流单号
        if ($row['status'] != 2) {
            $this->error('只有状态为2的订单才能打印标签');
        }

        if (empty($row['wldh'])) {
            $this->error('订单物流单号为空，无法打印标签');
        }

        // 根据物流商类型获取打印URL
        $printUrl = $this->getPrintUrl($row);

        $this->view->assign("row", $row);
        $this->view->assign("printUrl", $printUrl);
        return $this->view->fetch();
    }

    /**
     * 根据物流商获取打印URL
     */
    private function getPrintUrl($row)
    {
        $wl_type = $this->getLogisticsType($row['country_code']);

        if ($wl_type == 0) {
            // UBI物流打印URL
            return "https://ubi.example.com/print?tracking=" . $row['wldh'];
        } elseif ($wl_type == 3) {
            // 云途物流打印URL
            return "https://omsapi.uat.yunexpress.com/print?tracking=" . $row['wldh'];
        } else {
            // 默认打印URL
            return "#";
        }
    }

    /**
     * 根据国家代码获取物流类型
     * @param string $country_code 国家代码
     * @return int 物流类型 (-1: 未配置, 0: UBI物流, 3: 云途物流)
     */
    private function getLogisticsType($country_code)
    {
        try {
            // 查询国家表获取物流类型
            $country = Db::table('fa_country')->where('code', $country_code)->find();

            if ($country && isset($country['wl_type'])) {
                return intval($country['wl_type']);
            }

            // 如果没有找到国家或没有配置物流类型，返回-1表示未配置
            return -1;
        } catch (\Exception $e) {
            // 发生错误时返回-1
            \think\Log::write('获取物流类型失败: ' . $e->getMessage(), 'error');
            return -1;
        }
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            // 获取搜索模式
            $search_mode = $this->request->get('search_mode', '1');

            // 根据搜索模式预先设置查询条件
            $status_condition = null;
            if ($search_mode == '2') { // 发货模式
                // 只查询已经准备好发货的订单（状态为2）
                // 明确指定status列属于fa_order表，避免歧义
                $status_condition = ['fa_order.status', '=', 2];
            }

            // 获取搜索文本
            $search = $this->request->get('search', '');

            // 记录搜索参数到日志
            \think\Log::write('搜索参数: search=' . $search . ', search_mode=' . $search_mode, 'debug');

            // 构建查询参数
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 如果$where是闭包函数，我们需要创建一个新的闭包来包含原始闭包和额外的条件
            if ($status_condition && is_callable($where)) {
                $originalWhere = $where;
                $where = function ($query) use ($originalWhere, $status_condition) {
                    // 先应用原始的where条件
                    call_user_func($originalWhere, $query);
                    // 再应用额外的状态条件，确保使用完全限定的表名
                    $query->where('fa_order.status', '=', 2);
                };
            } elseif ($status_condition && is_array($where)) {
                // 如果$where是数组，直接添加条件
                $where[] = $status_condition;
            }

            // 如果是按订单编号搜索，优先使用精确匹配
            if ($search && is_string($search) && strlen($search) >= 5) {
                // 检查是否是订单编号格式（通常订单编号有特定格式）
                if (preg_match('/^[A-Za-z0-9-]+$/', $search)) {
                    // 记录订单编号搜索到日志
                    \think\Log::write('按订单编号搜索: ' . $search, 'debug');

                    // 创建一个新的闭包来处理订单编号搜索
                    if (is_callable($where)) {
                        $originalWhere = $where;
                        $where = function ($query) use ($originalWhere, $search) {
                            // 先应用原始的where条件
                            call_user_func($originalWhere, $query);
                            // 添加订单编号条件，使用OR连接以支持模糊匹配
                            $query->whereOr('fa_order.ddbh', 'LIKE', "%{$search}%");
                        };
                    } elseif (is_array($where)) {
                        // 如果$where是数组，添加OR条件
                        $where[] = ['fa_order.ddbh', 'LIKE', "%{$search}%"];
                    }
                }
            }

            $list = $this->model
                ->with(['admin', 'store', 'wuliu'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {

                $row->getRelation('admin')->visible(['nickname']);
                $row->getRelation('store')->visible(['name']);
                $row->getRelation('wuliu')->visible(['status']);
                if ($row['status'] == 3) {
                    Db::table('fa_ordergoods')->where('order_id', $row['id'])->update(['status' => 3, 'sfdy' => 1, 'sfqg' => 1]);
                }
                if ($row['status'] < 2 && $row['khname']) {
                    $datajson = json_decode($row['datajson'], true);
                    $wwc = 0;
                    if ($row['datajson'] && count($datajson)) {
                        foreach ($datajson as $kk => $vv) {
                            if ($vv['sfdy'] == 0 || $vv['sfqg'] == 0) {
                                $wwc++;
                            }
                        };
                    }
                    if ($wwc == 0) {
                        $this->model->where('id', $row['id'])->update(['status' => 2]);
                    }
                }

                // 处理ddbh中的中文字符
                if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $row['ddbh'])) {
                    // 使用日期替换中文字符
                    $row['ddbh'] = preg_replace('/[\x{4e00}-\x{9fa5}]/u', date('Ymd'), $row['ddbh']);
                }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        $this->assignconfig('gid', $this->gid);

        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $params['admin_id'] = $this->auth->id;

            $datajson = json_decode($params['datajson'], true);
            $wwc = 0;
            $names_arr = [];
            foreach ($datajson as $k => $list) {
                $names_arr[] = $list['name'];
                if ($list['sfdy'] == 0 || $list['sfdy'] == 0) {
                    $wwc++;
                }
            }

            $params['names'] = implode(",", $names_arr);
            $result = $this->model->allowField(true)->save($params);
            $lastid = Db::table('fa_order')->getLastInsID();
            $this->get_ordergoods($lastid);

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            if ($row['status'] == 3) {
                throw new Exception("订单已发货，不可更改");
                // $this->error(__('订单已发货，不可更改'));
            }
            if ($row['status'] == 4) {
                throw new Exception("订单已取消，不可更改");
                // $this->error(__('订单已取消，不可更改'));
            }
            if ($row['status'] == 5) {
                throw new Exception("订单已退款，不可更改");
                // $this->error(__('订单已退款，不可更改'));
            }

            $datajson = json_decode($params['datajson'], true);
            $wwc = 0;
            $names_arr = [];
            foreach ($datajson as $k => $list) {
                $names_arr[] = $list['name'];
                if ($list['sfdy'] == 0 || $list['sfqg'] == 0) {
                    $wwc++;
                }
            }

            if (!$row['khname']) {
                $params['status'] = 0;
            } elseif ($row['status'] < 3 && $row['khname'] && !$wwc) {
                $params['status'] = 2;
            } elseif ($row['status'] < 3 && $row['khname'] && $wwc) {
                $params['status'] = 1;
            }

            $params['names'] = implode(",", $names_arr);
            $result = $row->allowField(true)->save($params);
            $this->get_ordergoods($row['id']);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                Db::table('fa_ordergoods')->where('order_id', $item['id'])->delete();
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }


    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        $datajson = json_decode($row['datajson'], true);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (false === $this->request->isPost()) {
            $store_row = Db::table('fa_store')->where('id', $row['store_id'])->find();
            $data = [];
            if ($row['datajson']) {
                foreach ($datajson as $k => $list) {
                    list($aid, $sid) = explode("_", $list['sku']);
                    // $goods_row = Db::table('fa_product_sale')->where('id',$aid)->find();
                    $list['tsyqimgs'] = $list['tsyqimgs'] ? explode(",", $list['tsyqimgs']) : '';
                    $data[$k] = $list;
                }
            }
            $wuliu_row = Db::table('fa_wuliu')->where('order_id', $row['id'])->find();
            $wljson = isset($wuliu_row) && $wuliu_row['datajson'] ? json_decode($wuliu_row['datajson'], true) : [];

            $siteconfig = Db::table('fa_config')->where('name', 'wls')->find();
            $wls = json_decode($siteconfig['value'], true);
            $this->view->assign('row', $row);
            $this->view->assign('store', $store_row);
            $this->view->assign('data', $data);
            $this->view->assign('wljson', $wljson);
            $this->view->assign('wls', $wls);
            return $this->view->fetch();
        }

    }

    /**
     * 翻译物流信息
     */
    public function translate($ids = null)
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid request method'));
        }

        // 从POST数据中获取ids参数
        if ($ids === null) {
            $ids = $this->request->post('ids');
        }

        if (empty($ids)) {
            $this->error('订单ID不能为空');
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $wuliu_row = Db::table('fa_wuliu')->where('order_id', $row['id'])->find();
        $wljson = isset($wuliu_row) && $wuliu_row['datajson'] ? json_decode($wuliu_row['datajson'], true) : [];
        if (!$wljson) {
            $this->error('没有物流信息需要翻译');
        }

        try {
            // 检查是否有需要翻译的内容
            $hasTranslatableContent = false;
            if (isset($wljson['data'][0]['details'])) {
                $hasTranslatableContent = true;
            } elseif (isset($wljson['data'][0]['events'])) {
                $hasTranslatableContent = true;
            }

            if (!$hasTranslatableContent) {
                $this->error('物流数据格式不支持翻译');
            }

            $tranclate = new BaiduTranslate();
            \think\Log::write('BaiduTranslate实例创建成功 - 订单ID: ' . $ids, 'debug');

            // 先测试一个简单的翻译
            try {
                $testResult = $tranclate->translate('hello world', 'en', 'zh');
                \think\Log::write('翻译测试结果: ' . json_encode($testResult), 'debug');
            } catch (\Exception $e) {
                \think\Log::write('翻译测试失败: ' . $e->getMessage(), 'error');
                $this->error('翻译服务不可用：' . $e->getMessage());
            }

            if (isset($wljson['data'][0]['details'])) {
                \think\Log::write('检测到details格式物流数据 - 订单ID: ' . $ids, 'debug');
                // 处理乐天物流格式
                $textsToTranslate = [];
                $textMapping = [];

                foreach ($wljson['data'][0]['details'] as $k => $v) {
                    // 收集track_location
                    if (!empty($v['track_location'])) {
                        $key = 'loc_' . $k;
                        $textsToTranslate[$key] = $v['track_location'];
                        $textMapping[$key] = ['index' => $k, 'field' => 'track_location'];
                    }

                    // 收集track_description_en
                    if (!empty($v['track_description_en'])) {
                        $key = 'desc_en_' . $k;
                        $textsToTranslate[$key] = $v['track_description_en'];
                        $textMapping[$key] = ['index' => $k, 'field' => 'track_description_en'];
                    }

                    // 收集track_description
                    if (!empty($v['track_description'])) {
                        $key = 'desc_' . $k;
                        $textsToTranslate[$key] = $v['track_description'];
                        $textMapping[$key] = ['index' => $k, 'field' => 'track_description'];
                    }
                }
                // 执行翻译
                if (!empty($textsToTranslate)) {
                    $this->translateTexts($textsToTranslate, $textMapping, $wljson, $tranclate, 'details');
                }
            } elseif (isset($wljson['data'][0]['events'])) {
                // 处理UBI物流格式
                $textsToTranslate = [];
                $textMapping = [];

                foreach ($wljson['data'][0]['events'] as $k => $v) {
                    if (!empty($v['activity'])) {
                        $key = 'activity_' . $k;
                        $textsToTranslate[$key] = $v['activity'];
                        $textMapping[$key] = ['index' => $k, 'field' => 'activity'];
                    }
                }

                // 执行翻译
                if (!empty($textsToTranslate)) {
                    $this->translateTexts($textsToTranslate, $textMapping, $wljson, $tranclate, 'events');
                }
            }

        } catch (\Exception $e) {
            // 记录详细的错误信息
            \think\Log::write('翻译过程异常 - 订单ID: ' . $ids . ', 错误: ' . $e->getMessage(), 'error');
            $this->error('翻译失败：' . $e->getMessage());
        }

        // 返回翻译后的物流信息（移到try-catch外面）
        $this->success('翻译成功', '', ['wljson' => $wljson]);

    }

    /**
     * 申请单号页面
     */
    public function applynumber($ids = null)
    {
        $row = $this->model->get($ids);

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 检查是否已有发货信息
        if (!empty($row['wls']) && !empty($row['wldh'])) {
            $this->error('该订单已有发货信息，无需申请单号');
        }

        $countryModel=new \app\admin\model\Country();
        //将$row['country']转化成大写

        $countryInfo=$countryModel->where('ename',strtoupper($row['country']))->find();
        if(!$countryInfo){
            $row['country_code']='';
            $wl_type = -1; // 未找到国家信息
        }else{
            $row['country_code']=$countryInfo['sxname'];
            $wl_type = $countryInfo['wl_type'];
        }

        // 检查物流配置
        if($wl_type == -1) {
            $this->error('该国家尚未配置物流，请先在国家管理中配置物流');
        }
        if ($this->request->isAjax()) {
            $params = $this->request->post("row/a");

            if (empty($params)) {
                $this->error(__('Parameter %s can not be empty', ''));
            }

            // 验证必填字段
            if($wl_type==0){
                $required_fields = ['service_code', 'english_name', 'weight', 'weight_unit', 'value', 'currency', 'address_line1', 'sender_name', 'sender_email', 'sender_postcode', 'sender_country', 'sender_vendorid'];
            }else{
                $required_fields = ['service_code', 'english_name', 'weight', 'weight_unit', 'value', 'currency', 'address_line1', 'sender_name', 'sender_email', 'sender_postcode', 'sender_country'];
            }


            foreach ($required_fields as $field) {
                if (empty($params[$field])) {
                    return json([
                        'code' => 0,
                        'msg' => '字段 ' . $field . ' 不能为空',
                        'data' => null
                    ]);
                }
            }

            try {
                // 根据物流类型调用不同的API
                $trackingNumber = '';
                $wlsName = '';
                if($wl_type == 0) {
                    // UBI物流
                    $trackingNumber = $this->applyUbiLogistics($row, $params, $wl_type);
                    $wlsName = 'UBI国际物流';
                } elseif($wl_type == 3) {
                    // 云途物流
                    $trackingNumber = $this->applyYuntuLogistics($row, $params, $wl_type);
                    $wlsName = '云途物流';
                } else {
                    return json([
                        'code' => 0,
                        'msg' => '不支持的物流类型',
                        'data' => null
                    ]);
                }

                if(empty($trackingNumber)) {
                    return json([
                       'code'=>0,
                       'msg'=>'获取单号失败',
                       'data'=>null
                    ]);
                }

                // 更新订单信息
                $updateData = [
                    'wldh' => $trackingNumber,
                    'wls' => $wlsName,
                    'status' => 2
                ];

                $result = $row->allowField(['wldh', 'wls', 'status'])->save($updateData);

                if ($result !== false) {
                    return json([
                        'code' => 1,
                        'msg' => '申请单号成功',
                        'data' => ['tracking_number' => $trackingNumber]
                    ]);
                } else {
                    return json([
                        'code' => 0,
                        'msg' => '申请单号失败，请重试',
                        'data' => null
                    ]);
                }

            } catch (\Exception $e) {
                return json([
                    'code' => 0,
                    'msg' => '申请单号失败：' . $e->getMessage(),
                    'data' => null
                ]);
            }
        }

        // 根据物流类型获取服务代码列表
        $serviceCodes = [];
        try {
            if($wl_type == 0) {
                // UBI物流
                $serviceCodes = $this->getUbiServiceCodes();
            } elseif($wl_type == 3) {
                // 云途物流
                $serviceCodes = $this->getYuntuServiceCodes($row['country_code']);
            }
        } catch (\Exception $e) {
            $serviceCodes = [];
        }
       // var_dump($row);
        $this->view->assign("row", $row);
        $this->view->assign("serviceCodes", $serviceCodes);
        $this->view->assign("wl_type", $wl_type); // 传递物流类型到模板
        return $this->view->fetch();
    }

    /**
     * 执行翻译文本
     */
    private function translateTexts($textsToTranslate, $textMapping, &$wljson, $tranclate, $type)
    {
        try {
            \think\Log::write('开始翻译文本 - 类型: ' . $type . ', 文本数量: ' . count($textsToTranslate), 'debug');
            \think\Log::write('待翻译文本: ' . json_encode($textsToTranslate), 'debug');

            // 将所有文本合并为一个字符串，用特殊分隔符分隔
            $separator = '|||';
            $combinedText = implode($separator, array_values($textsToTranslate));
            \think\Log::write('合并后的文本: ' . $combinedText, 'debug');

            // 尝试一次性翻译
            try {

                $translated = $tranclate->translate($combinedText, 'auto', 'zh');

                // 分割翻译结果
                $translatedArray = explode($separator, $translated);
                $textKeys = array_keys($textsToTranslate);

                // 将翻译结果映射回原始键
                foreach ($translatedArray as $index => $translatedText) {
                    if (isset($textKeys[$index])) {
                        $key = $textKeys[$index];
                        $textsToTranslate[$key] = !empty(trim($translatedText)) ? $translatedText : $textsToTranslate[$key];
                    }
                }
            } catch (\Exception $e) {
                // 如果一次性翻译失败，回退到单独翻译
                foreach ($textsToTranslate as $key => $text) {
                    try {
                        if (!empty(trim($text))) {
                            $translated = $tranclate->translate($text, 'auto', 'zh');
                            if (!empty(trim($translated))) {
                                $textsToTranslate[$key] = $translated;
                            } else {
                                $textsToTranslate[$key] = $text; // 保留原文
                            }
                            usleep(100000); // 暂停0.1秒
                        }
                    } catch (\Exception $e) {
                        $textsToTranslate[$key] = $text; // 保留原文
                    }
                }
            }

            // 将翻译结果分配回原始数据结构
            foreach ($textsToTranslate as $key => $translatedText) {
                if (isset($textMapping[$key])) {
                    $index = $textMapping[$key]['index'];
                    $field = $textMapping[$key]['field'];

                    // 确保索引和字段存在
                    if (isset($wljson['data'][0][$type][$index])) {
                        if (!empty(trim($translatedText))) {
                            $wljson['data'][0][$type][$index][$field] = $translatedText;
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            // 如果整体翻译过程出错，回退到单独翻译
            $this->fallbackTranslation($wljson, $tranclate);
        }
    }


    public function operate($oid = null, $key = null, $title = null)
    {
        $row = $this->model->get($oid);
        $datajson = json_decode($row['datajson'], true);
        $field = $title == '打印' ? 'sfdy' : 'sfqg';
        $wwc = 0;
        $status = $row['status'];
        foreach ($datajson as $k => $list) {
            if ($k == $key) {
                $datajson[$k][$field] = 1;
            }
        }
        foreach ($datajson as $k => $list) {
            if ($list['sfdy'] == 0 || $list['sfqg'] == 0) {
                $wwc++;
            }
        }
        if (!$wwc && $row['status'] != 0) {
            $status = 2;
        }

        foreach ($datajson as $k => $v) {
            list($pid, $sid) = explode("_", $v['sku']);
            $sn = $pid . '_' . $k;
            Db::table('fa_ordergoods')
                ->where('sn', $sn)
                ->update([
                    'order_id' => $row['id'],
                    'product_sale_id' => $pid,
                    'store_id' => $row['store_id'],
                    'sku_id' => $sid,
                    'name' => $v['name'],
                    'bmdk' => $v['bmdk'],
                    'zfdy' => $v['zfdy'],
                    'tsyqimages' => $v['tsyqimgs'],
                    'tsyq' => $v['tsyq'],
                    'sfdy' => $v['sfdy'],
                    'sfqg' => $v['sfqg'],
                    'ordertime' => $row['ordertime'],
                ]);
        }


        $datajson = json_encode($datajson);
        $result = Db::table('fa_order')->where('id', $oid)->update(['datajson' => $datajson, 'status' => $status]);

        $this->get_ordergoods($row['id']);

        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success('操作成功');
    }

    public function send($ids = null)
    {
        $row = $this->model->get($ids);
        $datajson = json_decode($row['datajson'], true);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (false === $this->request->isPost()) {
            if ($row['status'] == 3) {
                $this->error(__('该订单已发货'));
            }
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if ($params['wldh'] == false) {
            $this->error(__('物流单号不得为空'));
        }
        $params['status'] = 3;
        $params['fhtime'] = time();
        $result = $row->allowField(true)->save($params);

        //同步订单产品
        Db::table('fa_ordergoods')->where('order_id', $row['id'])->update(['status' => 3, 'sfdy' => 1, 'sfqg' => 1]);

        //头程
        $today = strtotime(date('Y-m-d', time()));
        $tcgl_row = Db::table('fa_tcgl')->where('createtime', $today)->where('wls', $params['wls'])->find();
        if (!$tcgl_row) {
            Db::table('fa_tcgl')->insert([
                'admin_id' => $this->auth->id,
                'wls' => $params['wls'],
                'order_ids' => $row['id'],
                'createtime' => $today
            ]);
        } else {
            $oids = explode(',', $tcgl_row['order_ids']);
            if (!in_array($row['id'], $oids)) {
                Db::table('fa_tcgl')->where('createtime', $today)->where('wls', $params['wls'])->update([
                    'order_ids' => $tcgl_row['order_ids'] . ',' . $row['id']
                ]);
            }

        }


        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success('发货成功');


    }


    public function send_bf($ids = null)
    {
        $row = $this->model->get($ids);
        $datajson = json_decode($row['datajson'], true);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (false === $this->request->isPost()) {
            $store_row = Db::table('fa_store')->where('id', $row['store_id'])->find();
            $data = [];
            foreach ($datajson as $k => $list) {
                list($aid, $sid) = explode("_", $list['sku']);
                // $goods_row = Db::table('fa_product_sale')->where('id',$aid)->find();
                $list['tsyqimgs'] = explode(",", $list['tsyqimgs']);
                $data[$k] = $list;
            }
            $this->view->assign('row', $row);
            $this->view->assign('store', $store_row);
            $this->view->assign('data', $data);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        $ids_arr = $this->request->post('ids/a');
        if (count($ids_arr) == false) {
            $this->error(__('补发产品不得为空'));
        }
        if ($params['reason'] == false) {
            $this->error(__('补发理由不得为空'));
        }
        //标注原订单已补发的产品
        Db::table('fa_order')->where('id', $row['id'])->update([
            'bfids' => json_encode($ids_arr)
        ]);

        $bfids_arr = $names_arr = [];
        foreach ($datajson as $k => $list) {
            if (in_array($k, $ids_arr)) {
                $list['sfdy'] = $list['sfqg'] = 0;
                $bfids_arr[] = $list;
                $names_arr[] = $list['name'];
            }
        }
        $names = implode(',', $names_arr);

        $result = Db::table('fa_order')->insert([
            'admin_id' => $row['admin_id'],
            'store_id' => $row['store_id'],
            'ddbh' => $row['ddbh'] . ' - BUFA',
            'names' => $names,
            'khname' => $row['khname'],
            'khaddr1' => $row['khaddr1'],
            'khaddr2' => $row['khaddr2'],
            'city' => $row['city'],
            'state' => $row['state'],
            'country' => $row['country'],
            'zipcode' => $row['zipcode'],
            'datajson' => json_encode($bfids_arr),
            'status' => 1,
            'ifjjlist' => $row['ifjjlist'],
            'info' => $row['info'],
            'ordertime' => $row['ordertime'],
            'ifdblist' => $row['ifdblist'],
            'lxfs' => $row['lxfs'],
            'ifbf' => 1,
            'reason' => $params['reason'],
            'oid' => $row['id'],
            'createtime' => time(),
            'updatetime' => time(),
        ]);
        $lastid = Db::table('fa_order')->getLastInsID();
        $this->get_ordergoods($lastid);


        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success('补发成功');


    }

    public function tongbu()
    {
        $bf_rows = $this->model->where('ifbf', 1)->select();
        if (count($bf_rows)) {
            foreach ($bf_rows as $row) {
                $ys_row = $this->model->where('id', $row['oid'])->find();
                $this->model->where('id', $row['id'])->update(['lxfs' => $ys_row['lxfs']]);
            }
        }
    }

    // 复制内容
    public function copy($key = null, $value = null)
    {
        if (false === $this->request->isPost()) {
            $this->view->assign('k', $key);
            $this->view->assign('value', htmlentities($value));

            return $this->view->fetch();
        } else {
            $params = $this->request->post('data/a');
            $params['fieldcon'] = htmlentities($params['fieldcon']);
            $params['fieldtxt'] = strip_tags($params['fieldcon']);
            $this->success('操作成功', null, $params);
            // print_r($params);
        }


    }

    // 选择产品
    public function selectpro($key = null, $store_id = null)
    {
        if (false === $this->request->isPost()) {

            $goods_rows = Db::table('fa_product_sale')->where('store_id', $store_id)->select();

            $this->view->assign('list', $goods_rows);
            $this->view->assign('k', $key);

            return $this->view->fetch();
        }


    }

    //选择规格
    public function selectsku($ids = null, $key = null)
    {
        if (false === $this->request->isPost()) {

            $goods_row = Db::table('fa_product_sale')->where('id', $ids)->find();
            $images = explode(',', $goods_row['stylelist']);
            $names = json_decode($goods_row['stylename']);
            $data = [];
            foreach ($images as $k => $img) {
                $data[] = [
                    'name' => $names[$k],
                    'image' => $img,
                ];
            }

            $this->view->assign('data', $data);
            $this->view->assign('row', $goods_row);
            $this->view->assign('k', $key);

            return $this->view->fetch();
        }

    }

    //选择结果
    public function selectresult($key = null, $pid = null, $id = null)
    {
        $goods_row = Db::table('fa_product_sale')->where('id', $pid)->find();
        $images = explode(',', $goods_row['stylelist']);
        $names = json_decode($goods_row['stylename']);
        $skuimg = $images[$id];
        $return = [
            'sku' => $pid . '_' . $id,
            'proname' => $goods_row['name'],
            'proimg' => $goods_row['images'],
            'skuname' => $names[$id],
            'skuimg' => $skuimg,
        ];
        $this->success('选择成功', '', $return);
    }

    // 特殊要求上传
    public function uploadtsyq($key = null, $oid = null)
    {
        if (false === $this->request->isPost()) {

            $imgs = '';
            if ($oid != 0) {
                $row = $this->model->get($oid);
                $datajson = json_decode($row['datajson'], true);
                $imgs = isset($datajson[$key]) ? $datajson[$key]['tsyqimgs'] : '';
            }


            $this->view->assign('k', $key);
            $this->view->assign('imgs', $imgs);

            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if ($params['images'] == false) {
            $this->error(__('图片不得为空'));
        }
        $return = [
            'key' => $key,
            'images' => $params['images']
        ];
        $this->success('上传成功', '', $return);

    }

    //取消和退款
    public function cancel_refund($status = null, $ids = null)
    {
        $result = Db::table('fa_order')->where('id', $ids)->update(['status' => $status]);
        Db::table('fa_ordergoods')->where('order_id', $ids)->update(['status' => $status]);
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 导入
     *
     * @return void
     * @throws PDOException
     * @throws BindParamException
     */
    public function import()
    {

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');

        $file = $params['file'];

        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }
        //实例化reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }

        //导入文件首行类型,默认是注释,如果需要使用字段名称请使用name
        $importHeadType = isset($this->importHeadType) ? $this->importHeadType : 'comment';

        $table = $this->model->getQuery()->getTable();
        $database = \think\Config::get('database.database');
        $fieldArr = [
            'Sale Date' => 'ordertime',
            'Order ID' => 'ddbh',
            'Full Name' => 'khname',
            'Street 1' => 'khaddr1',
            'Street 2' => 'khaddr2',
            'Ship City' => 'city',
            'Ship State' => 'state',
            'Ship Zipcode' => 'zipcode',
            'Ship Country' => 'country',
        ];


        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                $row = [];
                $temp = array_combine($fields, $values);
                foreach ($temp as $k => $v) {
                    if (isset($fieldArr[$k]) && $k !== '') {
                        $row[$fieldArr[$k]] = $v;
                    }
                }
                if ($row) {
                    $insert[] = $row;
                }
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
        if (!$insert) {
            $this->error(__('No rows were updated'));
        }
        try {
            //是否包含admin_id字段
            foreach ($insert as $k => $row) {
                $order_row = Db::table('fa_order')->where('ddbh', $row['ddbh'])->find();
                $insert[$k]['ordertime'] = strtotime($row['ordertime']);
                $insert[$k]['admin_id'] = $this->auth->id;
                $insert[$k]['store_id'] = $params['store_id'];
                if ($order_row) {
                    $insert[$k]['id'] = $order_row['id'];
                    $datajson = json_decode($order_row['datajson'], true);
                    $wwc = 0;
                    if ($order_row['datajson'] && count($datajson)) {
                        foreach ($datajson as $kk => $list) {
                            if ($list['sfdy'] == 0 || $list['sfqg'] == 0) {
                                $wwc++;
                            }
                        };
                    }
                    if ($wwc == 0 && $order_row['status'] == 0) {
                        $insert[$k]['status'] = 2;
                    } elseif ($wwc && $order_row['status'] == 0) {
                        $insert[$k]['status'] = 1;
                    }
                }
            }
            // print_r($insert);exit;
            $this->model->saveAll($insert);
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
            };
            $this->error($msg);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success();
    }

    public function explode($ids = null)
    {
        $rows = $this->model->where('id', 'in', $ids)->select();

        $data = [];
        $title = ['*订单号', '*店铺账号', '*sku', '属性(可填写SKU尺寸、颜色等)', '*数量（大于0的整数）', '*单价', '总运费', '币种（默认USD）', '买家指定物流', '发货仓库', '*买家姓名', '*地址1', '地址2', '城市', '*省/州', '*国家二字码', '邮编', '电话', '手机', 'E-mail', '买家税号', '门牌号', '公司名', '订单备注', '图片网址', '售出链接', '中文报关名', '英文报关名', '申报金额（USD）', '申报重量（g）', '材质', '用途', '海关编码', '报关属性', '卖家税号（IOSS）', '下单时间（北京时间）', '客服备注', '拣货备注']; // 导出的标题格式
        foreach ($rows as $t) {
            $country_row = Db::table('fa_country')->where('ename', strtoupper($t['country']))->find();
            $store_row = Db::table('fa_store')->where('id', $t['store_id'])->find();
            $data[] = [
                $t['ddbh'],
                '手工订单',
                'NAMEPUZZLE',
                '',
                1,
                10.00,
                '',
                '',
                '',
                '',
                $t['khname'],
                $t['khaddr1'],
                $t['khaddr2'],
                $t['city'],
                $t['state'] ?: $t['city'],
                isset($country_row) ? $country_row['sxname'] : $t['country'],
                $t['zipcode'],
                '',
                $t['lxfs'] ?: $t['ddbh'],
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ];
        }


        // Create new Spreadsheet object
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 方法一，使用 setCellValueByColumnAndRow
        //表头
        //设置单元格内容
        foreach ($title as $key => $value) {
            // 单元格内容写入
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        $row = 2; // 从第二行开始
        foreach ($data as $item) {
            $column = 1;
            foreach ($item as $value) {
                // 单元格内容写入
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column++;
            }
            $row++;
        }


        // 设置第一行的行高为 25
        $sheet->getRowDimension(1)->setRowHeight(35);

        // 获取第一行所有列（假设最多到第 26 列，即 Z 列）
        $columnCount = 35;
        for ($col = 1; $col <= $columnCount; $col++) {
            // 获取当前列的字母表示
            $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col);
            // 设置列宽为 100
            $sheet->getColumnDimension($columnLetter)->setWidth(20);
            // 获取当前单元格
            $cell = $sheet->getCell("{$columnLetter}1");
            // 设置单元格样式
            $style = $cell->getStyle();

            // 设置字体样式：加粗，字号 14
            $font = $style->getFont();
            // $font->setBold(true);
            $font->setSize(11);

            // 设置对齐方式：水平和垂直居中
            $alignment = $style->getAlignment();
            $alignment->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $alignment->setVertical(Alignment::VERTICAL_CENTER);
        }


        $name = date('YmdHis', time());
        // Redirect output to a client's web browser (Xlsx)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . urlencode($name) . '.xlsx"');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');

        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
        print_r($rows);
    }


    public function get_ordergoods($ids = null)
    {
        $row = $this->model->get($ids);
        // Db::table('fa_ordergoods')->where('order_id',$ids)->delete();
        $datajson = json_decode($row['datajson'], true);
        $sn_arr = [];
        foreach ($datajson as $k => $v) {
            list($pid, $sid) = explode("_", $v['sku']);
            $sn = $row['id'] . '_' . $k;
            $sn_arr[] = $sn;
            $ordergoods_row = Db::table('fa_ordergoods')->where('sn', $sn)->find();
            if ($ordergoods_row) {
                Db::table('fa_ordergoods')->where('sn', $sn)->update([
                    'order_id' => $row['id'],
                    'sn' => $sn,
                    'product_sale_id' => $pid,
                    'store_id' => $row['store_id'],
                    'sku_id' => $sid,
                    'name' => $v['name'],
                    'bmdk' => $v['bmdk'],
                    'zfdy' => $v['zfdy'],
                    'tsyqimages' => $v['tsyqimgs'],
                    'tsyq' => $v['tsyq'],
                    'sfdy' => $v['sfdy'],
                    'sfqg' => $v['sfqg'],
                    'ordertime' => $row['ordertime'],
                    'status' => $v['sfdy'] == 0 || $v['sfqg'] == 0 ? 0 : $ordergoods_row['status'],
                ]);
            } else {
                Db::table('fa_ordergoods')->insert([
                    'order_id' => $row['id'],
                    'sn' => $sn,
                    'product_sale_id' => $pid,
                    'store_id' => $row['store_id'],
                    'sku_id' => $sid,
                    'name' => $v['name'],
                    'bmdk' => $v['bmdk'],
                    'zfdy' => $v['zfdy'],
                    'tsyqimages' => $v['tsyqimgs'],
                    'tsyq' => $v['tsyq'],
                    'sfdy' => $v['sfdy'],
                    'sfqg' => $v['sfqg'],
                    'ordertime' => $row['ordertime'],
                ]);
            }

        }

        Db::table('fa_ordergoods')->where('order_id', $row['id'])->where('sn', 'not in', $sn_arr)->delete();


    }

    /**
     * UBI物流申请单号
     */
    private function applyUbiLogistics($row, $params, $wl_type)
    {
        $orderItems = [];
        if($params['weight_unit']=='kg'){
            $wheight=floatval($params['weight'])*1000;
        } else {
            $wheight=floatval($params['weight']);
        }
        $orderItems[]=[
            'itemNo'=>$row['ddbh'],
            'sku'=>$row['names'],
            'description'=>$params['english_name'],
            'nativeDescription'=>$params['name'],
            'originCountry'=>$params['country_code'],
            'itemCount'=>1,
            'unitValue'=>floatval($params['value']),
            'weight'=>$wheight,
        ];

        $orderData = [
            'referenceNo' => $row['ddbh'],
            'recipientName' => $params['recipient_name'] ?: $row['khname'],
            'addressLine1' => $params['address_line1'],
            'addressLine2' => $params['address_line2'] ?: '',
            'addressLine3' => $params['address_line3'] ?: '',
            'city' => $params['city'] ?: $row['city'],
            'state' => $params['state'] ?: $row['state'],
            'postcode' => $row['zipcode'],
            'country' => $params['country_code'],
            'weight' => floatval($params['weight']),
            'weightUnit' => $params['weight_unit'],
            'description' => $params['english_name'],
            'nativeDescription'=>$params['name'],
            'invoiceValue' => floatval($params['value']),
            'invoiceCurrency' => $params['currency'],
            'serviceCode' => $params['service_code'],
            'shipperName' => $params['sender_name'],
            'shipperEmail' => $params['sender_email'],
            'shipperCountry'=> $params['sender_country'],
            'shipperPostcode' => $params['sender_postcode'],
            'extendData'=>[
                "vendorid"=> $params['sender_vendorid']
            ],
            'orderItems'=>$orderItems
        ];

        // 调用UBI物流API
        $ubi = new \wuliu\Ubi();
        $result = $ubi->createOrder([$orderData]);
        $result = json_decode($result, true);

        // 检查API返回结果
        if(!$result) {
            throw new \Exception('物流API返回数据格式错误');
        }

        if($result['status'] == 'Failure'){
            $message = "申请单号失败：";
            if(!empty($result['errors'])){
                foreach ($result['errors'] as $k=>$v){
                    $errorDesc = !empty($v['messageCn']) ? $v['messageCn'] : (!empty($v['message']) ? $v['message'] : '未知错误');
                    $message .= "错误码{" . $v['code'] . "}-" . $errorDesc . "；";
                }
            } else {
                $message .= "未知错误";
            }
            throw new \Exception($message);
        }

        // 检查是否成功并获取单号
        if($result['status'] == 'Success' && !empty($result['data'])) {
            $trackingNumber = $result['data'][0]['trackingNo']??'';
            if(empty($trackingNumber)) {
                throw new \Exception('获取单号失败');
            }
            return $trackingNumber;
        }

        throw new \Exception('申请单号失败');
    }

    /**
     * 云途物流申请单号
     */
    private function applyYuntuLogistics($row, $params, $wl_type)
    {
        //var_dump($params);
        // 构建云途物流订单参数
        $yuntuParams = [
            'CustomerOrderNumber' => $row['ddbh'], // 客户订单号 如果包含中文则转换成拼音
            'ShippingMethodCode' => $params['service_code'], // 运输方式代码
            'PackageCount' => isset($params['package_count']) ? intval($params['package_count']) : 1, // 包裹数量
            'Weight' => floatval($params['weight']), // 包裹重量，单位Kg
            'Receiver' => [
                'CountryCode' => $params['country_code'], // 国家简码
                'FirstName' => $params['recipient_name'], // 收件人名字
                'Street' => $params['address_line1'] . ($params['address_line2'] ? ' ' . $params['address_line2'] : '') . ($params['address_line3'] ? ' ' . $params['address_line3'] : ''), // 收件人详细地址
                'City' => $params['city'], // 收件人城市
                'State' => $params['state'], // 收件人省/州
                'Zip' => isset($params['zip']) ? $params['zip'] : '', // 收件人邮编
                'Phone' => isset($params['recipient_phone']) ? $params['recipient_phone'] : '', // 收件人电话
            ],
            'Sender' => [
                'CountryCode' => isset($params['sender_country']) ? $params['sender_country'] : 'CN', // 发件人国家简码
                'FirstName' => isset($params['sender_name']) ? $params['sender_name'] : '', // 发件人名字
                'Street' => isset($params['sender_address']) ? $params['sender_address'] : '', // 发件人详细地址
                'City' => isset($params['sender_city']) ? $params['sender_city'] : '', // 发件人城市
                'State' => isset($params['sender_state']) ? $params['sender_state'] : '', // 发件人省/州
                'Zip' => isset($params['sender_postcode']) ? $params['sender_postcode'] : '', // 发件人邮编
                'Phone' => isset($params['sender_phone']) ? $params['sender_phone'] : '', // 发件人电话
            ],
            'Parcels' => [
                'EName' => $params['english_name'], // 商品英文名称
                'CName' => isset($params['name']) ? $params['name'] : $params['english_name'], // 商品中文名称
                'Quantity' => 1, // 商品数量
                'UnitPrice' => floatval($params['value']), // 商品单价
                'UnitWeight' => floatval($params['weight']), // 商品重量
                'CurrencyCode' => isset($params['currency']) ? $params['currency'] : 'USD', // 货币代码
            ]
        ];


        // 调用云途物流API
        $yuntu = new \wuliu\Yuntu();

        // 添加调试信息
        \think\Log::write('云途物流请求参数: ' . json_encode($yuntuParams, JSON_UNESCAPED_UNICODE), 'debug');

        $result = $yuntu->createOrder($yuntuParams);

        // 添加调试信息
        \think\Log::write('云途物流API返回: ' . $result, 'debug');

        $result = json_decode($result, true);
        //var_dump($result);
        // 检查API返回结果
        if(!$result) {
            throw new \Exception('云途物流API返回数据格式错误');
        }

        // 根据云途物流API的返回格式解析单号
        if(isset($result['Code']) && $result['Code'] === '0000' && isset($result['Item'])) {
            // 成功情况
            if(isset($result['Item'][0]['WayBillNumber'])) {
                return $result['Item'][0]['WayBillNumber'];
            } else {
                throw new \Exception('云途物流API返回数据中未找到单号信息');
            }
        } else {
            // 失败情况
            $errorMsg = '云途物流申请单号失败';
            if(isset($result['Message'])) {
                $errorMsg .= '：' . $result['Message'];
            } elseif(isset($result['Error'])) {
                $errorMsg .= '：' . $result['Error'];
            }
            throw new \Exception($errorMsg);
        }
    }

    /**
     * 获取UBI物流服务代码
     */
    private function getUbiServiceCodes()
    {
        $ubi = new \wuliu\Ubi();
        $serviceResult = $ubi->getServicesCateLog();
        $serviceData = json_decode($serviceResult, true);

        $serviceCodes = [];

        // 检查不同的数据结构
        if (isset($serviceData['data']) && is_array($serviceData['data'])) {
            // 如果data是数组，直接遍历
            foreach ($serviceData['data'] as $service) {
                if (isset($service['serviceCode']) && isset($service['serviceName'])) {
                    $serviceCodes[] = [
                        'code' => $service['serviceCode'],
                        'name' => $service['serviceName'],
                        'nativeName'=>$service['nativeName']
                    ];
                }
            }
        } elseif (isset($serviceData['data']) && is_string($serviceData['data'])) {
            // 如果data是字符串，再次解析
            $innerData = json_decode($serviceData['data'], true);
            if (isset($innerData['data']) && is_array($innerData['data'])) {
                foreach ($innerData['data'] as $service) {
                    if (isset($service['serviceCode']) && isset($service['serviceName'])) {
                        $serviceCodes[] = [
                            'code' => $service['serviceCode'],
                            'name' => $service['serviceName']
                        ];
                    }
                }
            }
        }

        // 如果没有获取到服务代码，使用默认值
        if (empty($serviceCodes)) {
            $serviceCodes = [];
        }

        return $serviceCodes;
    }

    /**
     * 获取云途物流服务代码
     */
    private function getYuntuServiceCodes($countryCode)
    {
        try {
            $yuntu = new \wuliu\Yuntu();
            // 可以根据需要传递国家代码，这里先不传递获取所有服务
            $result = $yuntu->GetShippingMethods($countryCode);

            $serviceData = json_decode($result, true);
            $serviceCodes = [];

            // 检查云途物流API返回的数据结构
            if (isset($serviceData['Code']) && $serviceData['Code'] === '0000' && isset($serviceData['Items'])) {
                foreach ($serviceData['Items'] as $service) {
                    if (isset($service['Code']) && isset($service['EName'])) {
                        $serviceCodes[] = [
                            'code' => $service['Code'],
                            'name' => $service['EName'],
                            'nativeName' => isset($service['CName']) ? $service['CName'] : $service['EName']
                        ];
                    }
                }
            }

            // 如果没有获取到服务代码，使用默认值
            if (empty($serviceCodes)) {
                $serviceCodes = [
                    ['code' => 'YT001', 'name' => 'Yuntu Standard', 'nativeName' => '云途标准服务'],
                    ['code' => 'YT002', 'name' => 'Yuntu Express', 'nativeName' => '云途快速服务']
                ];
            }

            return $serviceCodes;
        } catch (\Exception $e) {
            // 如果API调用失败，返回默认服务代码
            return [
                ['code' => 'YT001', 'name' => 'Yuntu Standard', 'nativeName' => '云途标准服务'],
                ['code' => 'YT002', 'name' => 'Yuntu Express', 'nativeName' => '云途快速服务']
            ];
        }
    }

}
