define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/index' + location.search,
                    add_url: 'order/add',
                    edit_url: 'order/edit',
                    del_url: 'order/del',
                    multi_url: 'order/multi',
                    import_url: 'order/import',
                    table: 'order',
                }
            });

            var table = $("#table");
			$(".btn-add").data("area", ["100%", "100%"]);
			$(".btn-edit").data("area", ["100%", "100%"]);
			Table.button.edit.extend = 'data-toggle="tooltip" data-area=\'["100%", "100%"]\'';

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
				sortOrder: 'asc',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
						{field: 'store.name', title: __('Store.name'), operate: 'LIKE'},
                        {field: 'ddbh', title: __('Ddbh'), operate: 'LIKE'},
						{field: 'names', title: __('Names'), operate: 'LIKE'},
                        {field: 'khname', title: __('Khname'), operate: 'LIKE'},
      //                   {field: 'khaddr1', title: __('Khaddr1'), operate: 'LIKE'},
						// {field: 'khaddr2', title: __('Khaddr2'), operate: 'LIKE'},
						// {field: 'city', title: __('City'), operate: 'LIKE'},
						// {field: 'state', title: __('State'), operate: 'LIKE'},
						// {field: 'country', title: __('Country'), operate: 'LIKE'},
						// {field: 'zipcode', title: __('Zipcode'), operate: 'LIKE'},
                        {field: 'status', title: __('订单状态'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3'),"4":__('Status 4'),"5":__('Status 5'),"6":__('Status 6')}, formatter: Table.api.formatter.status},
						{field: 'wuliu.status', title: __('物流状态'), searchList: {"0":__('已发货'),"1":__('已在途'),"2":__('已签收'),"3":__('待赔偿'),"4":__('已赔偿')}, formatter: Table.api.formatter.status},
                        {field: 'ifjjlist', title: __('Ifjjlist'), searchList: {"0":__('Ifjjlist 0'),"1":__('Ifjjlist 1')}, formatter: Table.api.formatter.normal},
						{field: 'ifdblist', title: __('Ifdblist'), searchList: {"0":__('Ifdblist 0'),"1":__('Ifdblist 1')}, formatter: Table.api.formatter.normal},
                        {field: 'info', title: __('Info'), operate: 'LIKE'},
						{field: 'ifbf', title: __('Ifbf'), searchList: {"0":__('Ifbf 0'),"1":__('Ifbf 1')}, formatter: Table.api.formatter.normal},
						{field: 'reason', title: __('Reason'), operate: 'LIKE'},
                        {field: 'ordertime', title: __('Ordertime'), operate:'RANGE', addclass:'datetimerange',datetimeFormat:"YYYY-MM-DD", autocomplete:false, formatter: Table.api.formatter.datetime},
						{field: 'wls', title: __('Wls'), operate: 'LIKE'},
						{field: 'wldh', title: __('Wldh'), operate: 'LIKE'},
						{field: 'fhtime', title: __('发货时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},


                        {
                            field: 'operate',
                            width: "150px",
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [

                                {
                                    name: 'detail',
                        			text: __('详情'),
                                    title: __('详情'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    icon: 'fa fa-list',
                        			extend:'data-area=["90%","90%"]',
                                    url: 'order/detail',
                                    callback: function (data) {
                                        Layer.msg("订单已发货");
										return false;
                                    }
                                },
								{
								    name: 'ajax',
									text: __('取消'),
								    title: __('取消'),
								    classname: 'btn btn-xs btn-warning btn-ajax',
								    icon: 'fa fa-list',
									confirm: __('确定取消该订单吗？'),
								    url: 'order/cancel_refund/status/4',
									visible :function(row){
										if(row.status <= 2 && Config.gid != 6){
											return true;
										}else{
											return false;
										}
									},
								    success: function (data, ret) {
										layer.msg(__('订单已取消！'));
										setTimeout(function(){
											window.location.reload();
										},1000)
								        return false;
								    },
								    error: function (data, ret) {
								        console.log(data, ret);
								        Layer.alert(ret.msg);
								        return false;
								    }
								},
								{
								    name: 'ajax',
									text: __('退款'),
								    title: __('退款'),
								    classname: 'btn btn-xs btn-warning btn-ajax',
								    icon: 'fa fa-list',
									confirm: __('确定退款该订单吗？'),
								    url: 'order/cancel_refund/status/5',
									visible :function(row){
										if(row.status <= 2 && Config.gid != 6){
											return true;
										}else{
											return false;
										}
									},
								    success: function (data, ret) {
										layer.msg(__('订单已退款！'));
										setTimeout(function(){
											window.location.reload();
										},1000)
								        return false;
								    },
								    error: function (data, ret) {
								        console.log(data, ret);
								        Layer.alert(ret.msg);
								        return false;
								    }
								},
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

			$(document).on("click", ".btn-singlesearch", function () {
			    var options = table.bootstrapTable('getOptions');
			    var queryParams = options.queryParams;
			    options.pageNumber = 1;
			    options.queryParams = function (params) {
			        //这一行必须要存在,否则在点击下一页时会丢失搜索栏数据
			        params = queryParams(params);

			        //如果希望追加搜索条件,可使用
			//         var filter = params.filter ? JSON.parse(params.filter) : {};
			//         var op = params.op ? JSON.parse(params.op) : {};
			//         filter.url = 'login';
			//         op.url = 'like';

			//         params.filter = JSON.stringify(filter);
			//         params.op = JSON.stringify(op);
					params.filter = {};
					params.op = {};
			        console.log(params);
			        return params;
			    };
			    table.bootstrapTable('refresh', {});
			    return false;
			});

			$(document).on('click','.btn-explode',function(){
				var ids = Table.api.selectedids(table);
				if(ids.length == false){
					layer.msg('选择项不得为空');
					return false;
				}
				var url = "order/explode/ids/"+ids.join(",");
				window.location.href = url;
			})

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 表格初始化完成后的回调
            table.on('load-success.bs.table', function (e, data) {
                console.log('表格加载成功，开始加载订单列表视图');

                // 强制显示工具栏
                $("#fixed-toolbar").css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // 刷新订单列表数据
                loadOrderListData(1);

                // 延迟再次确保工具栏可见
                setTimeout(function() {
                    $("#fixed-toolbar").css({
                        'display': 'block',
                        'visibility': 'visible',
                        'opacity': '1',
                        'position': 'sticky',
                        'top': '0',
                        'z-index': '1050'
                    });
                }, 500);
            });

            // 订单列表视图相关代码
            var orderListContainer = $("#order-list-container");
            var orderListPagination = $("#order-list-pagination");
            var currentPage = 1;
            var pageSize = 10;
            var totalPages = 0;

            // 加载订单列表数据
            function loadOrderListData(page) {
                currentPage = page;
                // 获取表格的查询参数
                var options = table.bootstrapTable('getOptions');
                var params = {
                    offset: (page - 1) * pageSize,
                    limit: pageSize,
                    search: options.searchText || $(".search-input").val() || '',
                    sort: options.sortName || 'id',
                    order: options.sortOrder || 'desc',
                    search_mode: window.currentSearchMode || $(".search-mode").val() || '1' // 默认为全局模式
                };

                // 获取筛选参数
                if (options.queryParams) {
                    var tmpParams = options.queryParams({
                        search: params.search,
                        sort: params.sort,
                        order: params.order,
                        offset: params.offset,
                        limit: params.limit,
                        search_mode: params.search_mode
                    });
                    if (tmpParams.filter) {
                        params.filter = tmpParams.filter;
                    }
                    if (tmpParams.op) {
                        params.op = tmpParams.op;
                    }
                }

                // 强制显示工具栏和订单列表容器
                $("#fixed-toolbar, #order-list-container").show().css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // 确保搜索参数正确传递
                console.log('发送搜索请求，参数:', params);

                $.ajax({
                    url: table.bootstrapTable('getOptions').url,
                    type: "get",
                    dataType: "json",
                    data: params,
                    success: function(data) {
                        console.log('搜索结果:', data);
                        renderOrderList(data.rows);
                        renderOrderListPagination(Math.ceil(data.total / pageSize));

                        // 数据加载完成后再次确保工具栏和订单列表容器可见
                        setTimeout(function() {
                            $("#fixed-toolbar, #order-list-container, .order-list-view").show().css({
                                'display': 'block',
                                'visibility': 'visible',
                                'opacity': '1'
                            });
                        }, 100);
                    },
                    error: function(xhr, status, error) {
                        console.error('搜索请求失败:', error);
                        Layer.msg('搜索请求失败，请重试');
                    }
                });
            }

            // 渲染订单列表
            function renderOrderList(data) {
                // 确保订单列表容器可见
                orderListContainer.show().css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // 清空订单列表容器
                orderListContainer.empty();

                if (data.length === 0) {
                    orderListContainer.html('<div class="text-center">没有找到匹配的记录</div>');
                    return;
                }

                $.each(data, function(i, row) {
                    var statusText = '';
                    var statusClass = '';

                    switch(row.status) {
                        case '0': statusText = '待匹配'; statusClass = 'default'; break;
                        case '1': statusText = '待处理'; statusClass = 'info'; break;
                        case '2': statusText = '待发货'; statusClass = 'success'; break;
                        case '3': statusText = '已发货'; statusClass = 'primary'; break;
                        case '4': statusText = '已取消'; statusClass = 'danger'; break;
                        case '5': statusText = '已退款'; statusClass = 'warning'; break;
                        case '6': statusText = '已完结'; statusClass = 'success'; break;
                        default: statusText = '未知状态'; statusClass = 'default';
                    }

                    // 创建订单项
                    var orderItem = $('<div class="order-item"></div>');

                    // 订单头部
                    var orderHeader = $('<div class="order-header"></div>');
                    orderHeader.append('<div class="order-header-item">订单编号: ' + row.ddbh + '</div>');
                    orderHeader.append('<div class="order-header-item">订单日期: ' + (row.ordertime ? new Date(row.ordertime*1000).toLocaleDateString() : '未知') + '</div>');
                    orderHeader.append('<div class="order-header-item">店铺: ' + (row.store && row.store.name ? row.store.name : '未知') + '</div>');

                    // 订单内容
                    var orderBody = $('<div class="order-body"></div>');
                    var order=$('<div class="order"></div>');
                    // 解析datajson获取产品信息
                    try {
                        // 默认产品图片
                        var defaultProductImage = '/assets/img/placeholder.png';

                        // 尝试解析datajson
                        if (row.datajson) {
                            console.log('datajson:', row.datajson);
                            var products = [];

                            try {
                                products = JSON.parse(row.datajson);
                            } catch (jsonError) {
                                console.log('JSON解析失败，尝试其他方式解析', jsonError);
                                // 有些情况下datajson可能是字符串形式的数组
                                if (typeof row.datajson === 'string' && row.datajson.indexOf('[') === 0) {
                                    try {
                                        // 尝试eval解析（不推荐，但在某些情况下可能有效）
                                        products = eval(row.datajson);
                                    } catch (evalError) {
                                        console.log('eval解析也失败', evalError);
                                    }
                                }
                            }

                            console.log('解析后的products:', products);

                            if (Array.isArray(products) && products.length > 0) {
                                console.log(products);
                                // 显示所有产品，每个产品一行
                                for (var i = 0; i < products.length; i++) {
                                    var product = products[i];

                                    // 为每个产品创建一个新的产品容器
                                    var productContainer = $('<div class="order-product" style=" margin-bottom: 15px;' + (i > 0 ? ' border-top: 1px dashed #ddd; padding-top: 15px;' : '') + '"></div>');

                                    // 产品图片
                                    var productImage = '/assets/img/placeholder.png';
                                    if (product.proimg) {
                                        productImage = product.proimg;
                                    }
                                    productContainer.append('<img src="' + productImage + '" class="product-image" alt="产品图片">');

                                    // 产品信息容器
                                    var productInfo = $('<div class="product-info"></div>');
                                    productContainer.append(productInfo);

                                    // 产品序号（如果有多个产品）
                                    if (products.length > 1) {
                                        var productNumberRow = $('<div class="product-info-row"></div>');
                                        productNumberRow.append('<div class="product-info-label">产品 #' + (i + 1) + ':</div>');
                                        productNumberRow.append('<div class="product-info-value">' + (product.sku || '') + '</div>');
                                        productInfo.append(productNumberRow);
                                    }

                                    // 定制名称
                                    var customNameRow = $('<div class="product-info-row"></div>');
                                    customNameRow.append('<div class="product-info-label">定制名称:</div>');
                                    if(product.name && product.name != ''){
                                        customNameRow.append('<div class="product-info-value">' + (product.name || '无') + ' <span class="product-copy-btn" data-content="' + (product.name || '') + '">复制</span></div>');
                                    }else{
                                        customNameRow.append('<div class="product-info-value">无</div>');
                                    }
                                    productInfo.append(customNameRow);

                                    // 背面刻字
                                    var backEngravingRow = $('<div class="product-info-row"></div>');
                                    backEngravingRow.append('<div class="product-info-label">背面刻字:</div>');
                                    if(product.bmdk && product.bmdk != ''){
                                        backEngravingRow.append('<div class="product-info-value">' + (product.bmdk || '无') + ' <span class="product-copy-btn" data-content="' + (product.bmdk || '') + '">复制</span></div>');
                                    }else{
                                        backEngravingRow.append('<div class="product-info-value">无</div>');
                                    }
                                    productInfo.append(backEngravingRow);

                                    // 祝福打印
                                    var wishPrintRow = $('<div class="product-info-row"></div>');
                                    wishPrintRow.append('<div class="product-info-label">祝福打印:</div>');
                                    if(product.zfdy && product.zfdy != ''){
                                        wishPrintRow.append('<div class="product-info-value">' + (product.zfdy || '无') + ' <span class="product-copy-btn" data-content="' + (product.zfdy || '') + '">复制</span></div>');
                                    }else{
                                        wishPrintRow.append('<div class="product-info-value">无</div>');
                                    }
                                    productInfo.append(wishPrintRow);

                                    // 特殊要求
                                    var specialRequestRow = $('<div class="product-info-row"></div>');
                                    specialRequestRow.append('<div class="product-info-label">特殊要求:</div>');
                                    if(product.tsyq && product.tsyq != ''){
                                        specialRequestRow.append('<div class="product-info-value">' + (product.tsyq || '无') + ' <span class="product-copy-btn" data-content="' + (product.tsyq || '') + '">复制</span></div>');
                                    }else{
                                        specialRequestRow.append('<div class="product-info-value">无</div>');
                                    }
                                    productInfo.append(specialRequestRow);

                                    // 打印和切割状态
                                    var statusRow = $('<div class="product-info-row"></div>');
                                    statusRow.append('<div class="product-info-label">状态:</div>');
                                    var statusValue = $('<div class="product-info-value"></div>');

                                    // 打印状态
                                    var printStatus = product.sfdy == 1 ? 'completed' : '';
                                    statusValue.append('<span class="print-status ' + printStatus + '">已打印</span>');

                                    // 切割状态
                                    var cutStatus = product.sfqg == 1 ? 'completed' : '';
                                    statusValue.append('<span class="print-status ' + cutStatus + '">已切割</span>');

                                    statusRow.append(statusValue);
                                    productInfo.append(statusRow);
                                    order.append(productContainer)
                                    // 将产品容器添加到订单内容中
                                    orderBody.append(order);
                                }
                            }
                        } else {
                            // 如果没有datajson，创建默认的产品容器
                            var defaultProductContainer = $('<div class="order-product"></div>');

                            // 添加默认产品图片
                            defaultProductContainer.append('<img src="' + defaultProductImage + '" class="product-image" alt="产品图片">');

                            // 创建默认产品信息
                            var defaultProductInfo = $('<div class="product-info"></div>');

                            // 添加产品名称
                            var nameRow = $('<div class="product-info-row"></div>');
                            nameRow.append('<div class="product-info-label">产品名称:</div>');
                            nameRow.append('<div class="product-info-value">' + (row.names || '未知产品') + '</div>');
                            defaultProductInfo.append(nameRow);

                            // 添加产品信息到容器
                            defaultProductContainer.append(defaultProductInfo);

                            // 添加到订单内容
                            orderBody.append(defaultProductContainer);
                        }



                    } catch (e) {
                        console.log('解析产品信息失败', e);
                        orderBody.append('<div>无法加载产品信息</div>');
                    }

                    // 客户信息
                    var customerInfo = $('<div class="customer-info"></div>');
                    customerInfo.append('<div class="customer-info-title">联系方式:</div>');
                    customerInfo.append('<div class="customer-info-content">' + (row.khname || '未知客户') + ' <a href="javascript:;" class="btn btn-xs btn-info edit-address-btn" data-id="' + row.id + '"><i class="fa fa-pencil"></i> 编辑</a></div>');

                    // 收货地址
                    var address = [];
                    if (row.khaddr1) address.push(row.khaddr1);
                    if (row.khaddr2) address.push(row.khaddr2);
                    if (row.city) address.push(row.city);
                    if (row.state) address.push(row.state);
                    if (row.country) address.push(row.country);
                    if (row.zipcode) address.push(row.zipcode);

                    if (address.length > 0) {
                        customerInfo.append('<div class="customer-info-title">收货地址:</div>');
                        customerInfo.append('<div class="customer-info-content">' + address.join(', ') + '</div>');
                    }

                    orderBody.append(customerInfo);

                    // 订单状态
                    var orderStatus = $('<div class="order-status ' + (row.status >= 2 ? 'completed' : '') + '"></div>');
                    var statusContent = $('<div style="display: flex; align-items: center;"></div>');
                    statusContent.append('<span><span class="order-status-label">状态:</span>' + statusText + '</span>');

                    // 如果发货信息为空，添加申请单号按钮
                    if ((!row.wls || row.wls === '') && (!row.wldh || row.wldh === '')) {
                        statusContent.append('<a href="javascript:;" class="btn btn-xs btn-info apply-number-btn" data-id="' + row.id + '" style="margin-left: 8px;"><i class="fa fa-plus"></i> 申请单号</a>');
                    }

                    orderStatus.append(statusContent);

                    // 物流信息
                    if (row.wls && row.wldh) {
                        var trackingDiv = $('<div class="order-tracking" style="display: flex; align-items: center; justify-content: space-between;"></div>');
                        var trackingInfo = $('<div></div>');
                        trackingInfo.append('<div>物流商: ' + row.wls + '</div>');
                        trackingInfo.append('<div>物流单号: <span class="order-tracking-number">' + row.wldh + '</span></div>');

                        trackingDiv.append(trackingInfo);

                        // 如果订单状态为2且物流单号不为空，显示打印按钮
                        if (row.status == 2 && row.wldh && row.wldh !== '') {
                            var printBtn = $('<a href="javascript:;" class="btn btn-xs btn-warning print-label-btn" data-id="' + row.id + '" style="margin-left: 8px;"><i class="fa fa-print"></i> 打印</a>');
                            trackingDiv.append(printBtn);
                        }

                        orderStatus.append(trackingDiv);
                    }

                    // 订单操作按钮
                    var orderActions = $('<div class="order-actions"></div>');

                    // 详情按钮 - 使用自定义类名，避免与原始按钮冲突
                    orderActions.append('<a href="javascript:;" class="btn btn-xs btn-primary custom-btn-dialog" data-url="order/detail/ids/' + row.id + '" data-title="详情" data-area=["95%","95%"]"><i class="fa fa-list"></i> 详情</a>');

                    // 编辑按钮 - 使用自定义类名，避免与原始按钮冲突
                    orderActions.append('<a href="javascript:;" class="btn btn-xs btn-success custom-btn-dialog" data-url="order/edit/ids/' + row.id + '" data-title="编辑" data-area=["95%","95%"]"><i class="fa fa-pencil"></i> 编辑</a>');

                    // 删除按钮 - 使用自定义类名
                    orderActions.append('<a href="javascript:;" class="btn btn-xs btn-danger custom-btn-ajax" data-url="order/del/ids/' + row.id + '" data-confirm="确定删除该订单吗？"><i class="fa fa-trash"></i> 删除</a>');

                    // 根据状态添加不同的按钮 - 使用自定义类名
                    if (row.status <= 2) {
                        orderActions.append('<a href="javascript:;" class="btn btn-xs btn-warning custom-btn-ajax" data-url="order/cancel_refund/status/4/ids/' + row.id + '" data-confirm="确定取消该订单吗？"><i class="fa fa-ban"></i> 取消</a>');
                        orderActions.append('<a href="javascript:;" class="btn btn-xs btn-danger custom-btn-ajax" data-url="order/cancel_refund/status/5/ids/' + row.id + '" data-confirm="确定退款该订单吗？"><i class="fa fa-reply"></i> 退款</a>');
                    }

                    // 添加订单ID作为data属性，方便选中操作
                    orderItem.attr('data-id', row.id);

                    // 组装订单项
                    orderItem.append(orderHeader).append(orderBody).append(orderStatus).append(orderActions);
                    orderListContainer.append(orderItem);

                    // 添加订单项点击事件，实现选中功能
                    orderItem.on('click', function(e) {
                        // 如果点击的是按钮，不触发选中
                        if ($(e.target).closest('a.btn').length || $(e.target).closest('button').length) {
                            return;
                        }

                        // 切换选中状态
                        $(this).toggleClass('selected');

                        // 更新表格的选中状态
                        var selectedIds = [];
                        orderListContainer.find('.order-item.selected').each(function() {
                            selectedIds.push($(this).data('id'));
                        });

                        // 更新表格的选中状态
                        table.bootstrapTable('checkBy', {field: 'id', values: selectedIds});
                    });
                });

                // 绑定事件
                require(['fast'], function(Fast) {
                    // 绑定自定义对话框按钮事件
                    orderListContainer.find('.custom-btn-dialog').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            // 阻止事件冒泡，避免触发原始按钮事件
                            e.preventDefault();
                            e.stopPropagation();

                            var that = this;
                            var url = $(that).data('url') || '';
                            var title = $(that).data('title') || '';
                            var area = $(that).data('area') || ['95%', '95%'];

                            // 确保area是数组格式
                            if (typeof area === 'string') {
                                try {
                                    area = JSON.parse(area.replace(/'/g, '"'));
                                } catch (e) {
                                    console.log('解析area失败', e);
                                    area = ['95%', '95%'];
                                }
                            }

                            console.log('打开对话框', url, title, area);
                            Fast.api.open(url, title, {area: area});
                            return false;
                        });
                    });

                    // 绑定自定义AJAX按钮事件
                    orderListContainer.find('.custom-btn-ajax').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            // 阻止事件冒泡，避免触发原始按钮事件
                            e.preventDefault();
                            e.stopPropagation();

                            var that = this;
                            var url = $(that).data('url') || '';
                            var confirm = $(that).data('confirm') || '';
                            if (confirm) {
                                Layer.confirm(confirm, function() {
                                    Fast.api.ajax({
                                        url: url,
                                        data: {}
                                    }, function(data, ret) {
                                        Layer.msg(ret.msg || '操作成功');
                                        loadOrderListData(currentPage);
                                        return false;
                                    });
                                });
                            } else {
                                Fast.api.ajax({
                                    url: url,
                                    data: {}
                                }, function(data, ret) {
                                    Layer.msg(ret.msg || '操作成功');
                                    loadOrderListData(currentPage);
                                    return false;
                                });
                            }
                            return false;
                        });
                    });

                    // 绑定复制按钮事件
                    orderListContainer.find('.product-copy-btn').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            var content = $(this).data('content');
                            if (!content) {
                                Layer.msg('没有可复制的内容');
                                return;
                            }

                            // 创建临时文本区域
                            var tempTextarea = $('<textarea>');
                            tempTextarea.val(content);
                            tempTextarea.css({
                                position: 'absolute',
                                left: '-9999px',
                                top: '0'
                            });
                            $('body').append(tempTextarea);

                            // 选择文本并复制
                            tempTextarea.select();
                            try {
                                var successful = document.execCommand('copy');
                                if (successful) {
                                    Layer.msg('复制成功: ' + content);
                                } else {
                                    Layer.msg('复制失败，请手动复制');
                                }
                            } catch (err) {
                                Layer.msg('复制失败: ' + err);
                            }

                            // 移除临时文本区域
                            tempTextarea.remove();
                        });
                    });

                    // 绑定编辑联系方式和收货地址按钮事件
                    orderListContainer.find('.edit-address-btn').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            var id = $(this).data('id');
                            var url = 'order/editaddress/ids/' + id;
                            var title = '编辑联系方式和收货地址';

                            Fast.api.open(url, title, {
                                area: ['80%', '75%'],
                                callback: function(data) {
                                    console.log('编辑地址回调数据:', data);
                                    if (data && data.result) {
                                        Layer.msg(data.message || data.msg || '保存成功');
                                        loadOrderListData(currentPage);
                                    }
                                }
                            });

                            return false;
                        });
                    });

                    // 绑定申请单号按钮事件
                    orderListContainer.find('.apply-number-btn').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            var id = $(this).data('id');
                            var url = 'order/applynumber/ids/' + id;
                            var title = '申请单号';

                            Fast.api.open(url, title, {
                                area: ['80%', '75%'],
                                callback: function(data) {
                                    // 申请成功后刷新订单列表
                                    loadOrderListData(currentPage);
                                }
                            });

                            return false;
                        });
                    });

                    // 绑定打印标签按钮事件
                    orderListContainer.find('.print-label-btn').each(function() {
                        $(this).unbind('click').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            var id = $(this).data('id');
                            var url = 'order/printlabel/ids/' + id;
                            var title = '打印标签';

                            Fast.api.open(url, title, {
                                area: ['80%', '75%'],
                                callback: function(data) {
                                    // 打印完成后可以刷新订单列表（可选）
                                    // loadOrderListData(currentPage);
                                }
                            });

                            return false;
                        });
                    });
                });
            }

            // 渲染分页
            function renderOrderListPagination(total) {
                totalPages = total;

                // 确保分页容器可见
                orderListPagination.show().css({
                    'display': 'block',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // 清空分页容器
                orderListPagination.empty();

                if (total <= 1) {
                    return;
                }

                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(total, startPage + 4);

                if (currentPage > 1) {
                    orderListPagination.append('<li><a href="javascript:;" data-page="' + (currentPage - 1) + '">&laquo;</a></li>');
                } else {
                    orderListPagination.append('<li class="disabled"><a href="javascript:;">&laquo;</a></li>');
                }

                for (var i = startPage; i <= endPage; i++) {
                    if (i === currentPage) {
                        orderListPagination.append('<li class="active"><a href="javascript:;" data-page="' + i + '">' + i + '</a></li>');
                    } else {
                        orderListPagination.append('<li><a href="javascript:;" data-page="' + i + '">' + i + '</a></li>');
                    }
                }

                if (currentPage < total) {
                    orderListPagination.append('<li><a href="javascript:;" data-page="' + (currentPage + 1) + '">&raquo;</a></li>');
                } else {
                    orderListPagination.append('<li class="disabled"><a href="javascript:;">&raquo;</a></li>');
                }

                // 绑定分页事件
                orderListPagination.find("a[data-page]").on("click", function() {
                    var page = $(this).data("page");
                    loadOrderListData(page);
                });
            }

            // 刷新按钮事件已移至initOrderListView函数中

            // 初始化订单列表视图
            function initOrderListView() {
                // 确保工具栏按钮可见
                $("#fixed-toolbar").show();

                // 强制显示工具栏
                setTimeout(function() {
                    $("#fixed-toolbar").css({
                        'display': 'block',
                        'visibility': 'visible',
                        'opacity': '1'
                    });
                }, 100);

                // 重新绑定工具栏按钮事件
                // 刷新按钮
                $(".btn-refresh").unbind('click').on("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // 先刷新表格，然后再刷新订单列表
                    table.bootstrapTable('refresh');

                    // 刷新完表格后，重新加载订单列表
                    setTimeout(function() {
                        loadOrderListData(1);
                    }, 500);

                    return false;
                });

                // 添加按钮
                $(".btn-add").unbind('click').on("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var url = 'order/add';
                    var title = '添加';
                    Fast.api.open(url, title, {area: ['95%', '95%']});

                    return false;
                });

                // 导入地址按钮
                $(".btn-dialog").unbind('click').on("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var url = $(this).data('url') || '';
                    var title = $(this).data('title') || $(this).attr('title') || '';
                    Fast.api.open(url, title);

                    return false;
                });

                // 订单导出按钮
                $(".btn-explode").unbind('click').on("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var ids = Table.api.selectedids(table);
                    if(ids.length == 0){
                        Layer.msg('请选择要导出的订单');
                        return false;
                    }
                    var url = "order/explode/ids/"+ids.join(",");
                    window.location.href = url;

                    return false;
                });

                // 绑定搜索按钮事件
                $(".search-btn").unbind('click').on("click", function() {
                    var searchText = $(".search-input").val();
                    var searchMode = $(".search-mode").val();

                    // 保存当前搜索模式到全局变量
                    window.currentSearchMode = searchMode;

                    // 更新表格的搜索文本
                    table.bootstrapTable('getOptions').searchText = searchText;

                    // 记录搜索参数
                    console.log('搜索参数: search=' + searchText + ', search_mode=' + searchMode);

                    // 如果是订单编号搜索，确保传递正确的参数
                    if (searchText && searchText.length >= 5 && /^[A-Za-z0-9-]+$/.test(searchText)) {
                        console.log('检测到可能是订单编号搜索');
                    }

                    // 刷新表格，传递搜索参数
                    table.bootstrapTable('refresh', {
                        query: {
                            search: searchText,
                            search_mode: searchMode
                        }
                    });

                    // 刷新完表格后，重新加载订单列表
                    setTimeout(function() {
                        loadOrderListData(1);
                    }, 500);
                });

                // 绑定搜索框回车事件
                $(".search-input").unbind('keypress').on("keypress", function(e) {
                    if (e.which === 13) {
                        $(".search-btn").trigger("click");
                        return false;
                    }
                });

                // 绑定清除搜索按钮事件
                $(".clear-search-btn").unbind('click').on("click", function() {
                    $(".search-input").val('');

                    // 更新表格的搜索文本
                    table.bootstrapTable('getOptions').searchText = '';

                    // 刷新表格，清除搜索参数
                    table.bootstrapTable('refresh', {
                        query: {
                            search: '',
                            search_mode: $(".search-mode").val() || '1'
                        }
                    });

                    // 刷新完表格后，重新加载订单列表
                    setTimeout(function() {
                        loadOrderListData(1);
                    }, 500);
                });

                // 初始化加载订单列表数据
                loadOrderListData(1);
            }

            // 直接调用初始化函数，确保工具栏按钮可见
            initOrderListView();
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		import: function () {
		    Controller.api.bindevent();
		},
		selectpro: function () {
		    Controller.api.bindevent();
		},
		selectsku: function () {
		    Controller.api.bindevent();
		},
		uploadtsyq: function () {
		    // Controller.api.bindevent();
			Form.api.bindevent($("form[role=form]"),function(data){
				layer.msg(123);
				console.log(data);
				var imgs_arr = data.images.split(",");
				var key = data.key;
				var html = '';
				for (var i = 0; i < imgs_arr.length; i++) {
					html += '<img src="'+imgs_arr[i]+'" class="imgicon" alt="">';
				}
				parent.$('.pro'+key).find(".yqimgs").html(html);
				parent.$('.pro'+key).find(".tsyqimgstxt").val(data.images);
				parent.$("[data-name='row[datajson]'] input:first-child").trigger("change");
			})
		},
		detail: function () {
			$(document).on('click','.btn-send',function(){
				var id = $(this).data('id');
				Fast.api.open('order/send/ids/'+id,'发货');
			})

			$(document).on('click','.btn-sendbf',function(){
				var id = $(this).data('id');
				Fast.api.open('order/send_bf/ids/'+id,'补发',{area:['100%','100%']});
			})

			Controller.api.bindevent();
		},
		send: function () {
			Form.api.bindevent($("form[role=form]"),function(){
				// parent.Fast.api.close('订单已发货');
				parent.parent.$("#table").bootstrapTable('refresh',{});
				parent.Fast.api.close(parent.Layer.getFrameIndex(window.name));
				// parent.$('.btn-send0').remove();
				// parent.$('#status').html('已发货');
				// parent.$('#wls').html('已发货');
				// parent.$('#status').html('已发货');
			})

		    // Controller.api.bindevent();
		},
		send_bf: function () {
		    Controller.api.bindevent();
		},
        editaddress: function () {
            Controller.api.editaddress();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            editaddress: function () {
                Form.api.bindevent($("form[role=form]"), function(data, ret) {
                    // 成功提交后的回调
                    console.log('编辑地址成功回调:', ret);
                    if (ret.hasOwnProperty("code") && ret.code === 1) {
                        parent.Layer.closeAll();
                        parent.Layer.msg(ret.message || ret.msg || '保存成功');
                        parent.$("#table").bootstrapTable('refresh');
                        // 如果是在自定义列表视图中，也刷新自定义列表
                        if (typeof parent.loadOrderListData === 'function') {
                            parent.loadOrderListData(1);
                        }
                        // 返回数据给回调函数
                        Fast.api.close({
                            result: true,
                            message: ret.message || ret.msg || '保存成功',
                            code: 1
                        });
                    } else {
                        Layer.alert(ret.msg);
                        return false;
                    }
                }, function(data, ret) {
                    // 失败提交后的回调
                    console.log('编辑地址失败回调:', ret);
                    Layer.alert(ret.msg);
                    return false;
                });
            }
        }
    };

	$(document).on("fa.event.appendfieldlist", '[data-name="row[datajson]"] .btn-append', function(e, obj){
	    //通用的表单组件事件绑定和组件渲染
	      Form.api.bindevent($(".fieldlist"));
		  Form.events.selectpage($(".fieldlist"));
		  $(".fieldlist .form-inline input:first-child").trigger("change");
	});

	$(document).on('click','.btn-copy',function(){
		var key = $(this).data('id');
		var field = $(this).data('value');
		var value = $(this).parents(".pro"+key).find("."+field+"con").val();
		console.log(key);
		console.log(value);
		$("#keyid").val(key);
		$("#field").val(field);
		$("#fieldcon").val(value);
		$("#fieldcon").trigger("change");
		$(".conbox,.bg").fadeIn();
	})

	$(document).on('click','.bg',function(){
		$(".conbox,.bg").fadeOut();
	})


	$(document).on('click','.selectpro',function(){
		var key = $(this).data('id');
		var store_id = $("#c-store_id").val();
		if(store_id == false){
			layer.msg('请先选择店铺！');
			return false;
		}
		Fast.api.open('order/selectpro/key/'+key+'/store_id/'+store_id,'产品选择',{area:['95%','95%']});
	})
	$(document).on('click','.selectsku',function(){
		var id = $(this).data('id');
		var key = $(this).data('key');
		Fast.api.open('order/selectsku/ids/'+id+'/key/'+key,'规格选择',{area:['95%','95%']});
	})

	$(document).on('click','.uploadtsyq',function(){
		var key = $(this).data('id');
		var oid = $(this).data('oid');
		console.log(oid);
		Fast.api.open('order/uploadtsyq/key/'+key+'/oid/'+oid,'特殊要求');
	})

	$(document).on('click','.selectresult',function(){
		var pid = $(this).data('pid');
		var key = $(this).data('key');
		var id = $(this).data('id');
		$.ajax({
			url: "order/selectresult",
			type: "get",
			data: {key:key,pid:pid,id:id},
			dataType: 'json',
			success: function(data) {
				console.log(data);
				Fast.api.close();
				parent.Fast.api.close();
				parent.parent.layer.msg(data.msg);
				parent.parent.$('.pro'+key).find(".proimg").attr('src',data.data.proimg);
				parent.parent.$('.pro'+key).find(".skuimg").attr('src',data.data.skuimg);
				parent.parent.$('.pro'+key).find(".proimgtxt").val(data.data.proimg);
				parent.parent.$('.pro'+key).find(".skuimgtxt").val(data.data.skuimg);
				parent.parent.$('.pro'+key).find(".sku").val(data.data.sku);
				parent.parent.$("[data-name='row[datajson]'] input:first-child").trigger("change");
			},
			error: function(a) {
				console.log(a);
			}

		});
	})

	//复制内容
	$(document).on('click','.btn-copy-submit',function(){
		var key = $("#keyid").val();
		var field = $("#field").val();
		$.ajax({
			url: "order/copy",
			type: "post",
			data: $("#copy-form").serialize(),
			dataType: 'json',
			success: function(data) {
				console.log(data);
				if(data.code){
					$('.pro'+key).find("."+field+"con").val(data.data.fieldcon);
					$('.pro'+key).find("."+field+"txt").val(data.data.fieldtxt);
					$("[data-name='row[datajson]'] input:first-child").trigger("change");
					$(".conbox,.bg").fadeOut();
				}else{
					layer.msg('提取错误');
				}

			},
			error: function(a) {
				console.log(a);
			}

		});
	})



    return Controller;
});



function operate(oid,key,title){
	layer.open({
		title: '提示',
		content: '是否确定已'+title+'？',
		skin: 'demo-class',
		btn: ['确定', '取消'],
		yes: function(index, layero) {
			$.ajax({
				url: "order/operate",
				type: "get",
				data: {oid:oid,key:key,title:title},
				dataType: 'json',
				success: function(data) {
					console.log(data);
					layer.msg(data.msg);
					parent.$(".btn-refresh").trigger("click");
					if(data.code){
						setTimeout(function() {
							window.location.reload();
						}, 1000);
					}

				},
				error: function(a) {
					console.log(a);
				}

			});
		}
	});
}