<?php
/*
*Created By PhpStorm
*File:Yuntu.php
*User:尊杨科技
*Date:2025/6/3
*/

namespace wuliu;

class Yuntu
{

    protected $apiKey='';
    protected $secretKey='';
    protected $url='';
    public function __construct()
    {
        $this->apiKey="ITC0893791";
        $this->secretKey='axzc2utvPbfc9UbJDOh+7w==';
        $this->url='http://omsapi.uat.yunexpress.com';
    }

    private function buildHeaders(){
        return [
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Basic ' . base64_encode($this->apiKey.'&'.$this->secretKey),
            'charset: UTF-8'
        ];
    }

    /**
     * 创建订单
     * @param $params
     * @return bool|string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2025/6/3
     */
    public function createOrder($params)
    {
        // 处理CustomerOrderNumber，替换中文字符为日期数字
        $customerOrderNumber = $this->sanitizeOrderNumber($params['CustomerOrderNumber']);

        // 根据官方样例，构建正确的订单参数
        $order_param = [
            'CustomerOrderNumber' => $customerOrderNumber, //客户订单号
            'ShippingMethodCode' => $params['ShippingMethodCode'], //运输方式代码
            'PackageCount' => intval($params['PackageCount']), //包裹数量
            'Weight' => floatval($params['Weight']), //包裹重量,单位g
            'Receiver' => [
                'FirstName' => $params['Receiver']['FirstName'], //收件人名字
                'CountryCode' => $params['Receiver']['CountryCode'], //国家简码
                'State' => $params['Receiver']['State'], //收件人省/州
                'City' => $params['Receiver']['City'], //收件人城市
                'Street' => $params['Receiver']['Street'], //收件人详细地址
                'Zip' => $params['Receiver']['Zip'], //收件人邮编
                'Phone' => $params['Receiver']['Phone'], //收件人电话
                'Company' => isset($params['Receiver']['Company']) ? $params['Receiver']['Company'] : '', //公司名称
            ],
            'Parcels' => [[
                'CName' => $params['Parcels']['CName'], //商品中文名称
                'EName' => $params['Parcels']['EName'], //商品英文名称
                'SKU' => isset($params['Parcels']['SKU']) ? $params['Parcels']['SKU'] : 'DEFAULT-SKU', //商品SKU
                'Quantity' => intval($params['Parcels']['Quantity']), //商品数量
                'UnitPrice' => floatval($params['Parcels']['UnitPrice']), //商品单价
                'CurrencyCode' => $params['Parcels']['CurrencyCode'] ?? "USD", //货币代码
                'UnitWeight' => floatval($params['Parcels']['UnitWeight']), //商品重量
            ]],
            'OrderExtra' => [
                [
                    'ExtraCode' => 'V1',
                    'ExtraName' => '云途预缴'
                ]
            ]
        ];

        $requestType = 'POST';
        $method = '/api/WayBill/CreateOrder';
        $header = $this->buildHeaders();

        // 重要：根据官方样例，需要将订单参数包装在数组中
        $request_data = [$order_param];

        // 临时调试：输出请求参数
        error_log('云途物流请求参数: ' . json_encode($request_data, JSON_UNESCAPED_UNICODE));

        return $this->sendRequest($requestType, $method, $header, $request_data);
    }

    /**
     * 处理订单号，替换中文字符为日期数字
     * @param string $orderNumber 原始订单号
     * @return string 处理后的订单号
     */
    private function sanitizeOrderNumber($orderNumber)
    {
        // 生成当前日期时间数字：年月日时分秒
        $dateNumber = date('YmdHis');

        // 定义常见的中文关键词及其对应的数字替换
        $chineseReplacements = [
            '补发' => $dateNumber . '01', // 补发用日期+01
            '重发' => $dateNumber . '02', // 重发用日期+02
            '退换' => $dateNumber . '03', // 退换用日期+03
            '换货' => $dateNumber . '04', // 换货用日期+04
            '补货' => $dateNumber . '05', // 补货用日期+05
            '重新' => $dateNumber . '06', // 重新用日期+06
            '再次' => $dateNumber . '07', // 再次用日期+07
            '二次' => $dateNumber . '08', // 二次用日期+08
        ];

        // 替换已知的中文关键词
        $sanitized = $orderNumber;
        foreach ($chineseReplacements as $chinese => $replacement) {
            if (strpos($sanitized, $chinese) !== false) {
                $sanitized = str_replace($chinese, $replacement, $sanitized);
                break; // 只替换第一个匹配的关键词
            }
        }

        // 如果还有其他中文字符，用正则表达式替换为日期数字
        if (preg_match('/[\x{4e00}-\x{9fff}]/u', $sanitized)) {
            // 移除所有中文字符，并在末尾添加日期数字
            $sanitized = preg_replace('/[\x{4e00}-\x{9fff}]/u', '', $sanitized);
            $sanitized = rtrim($sanitized, '_-') . '_' . $dateNumber;
        }

        // 确保订单号不超过50个字符（云途API限制）
        if (strlen($sanitized) > 50) {
            $sanitized = substr($sanitized, 0, 30) . '_' . $dateNumber;
        }

        // 记录日志以便调试
        if ($sanitized !== $orderNumber) {
            error_log("订单号处理: {$orderNumber} -> {$sanitized}");
        }

        return $sanitized;
    }

    /**
     * 获取国家简码
     * @return bool|string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2025/6/3
     */
    public function getCountry()
    {
       $requestType='GET';
       $method='/api/Common/GetCountry';
       $header=$this->buildHeaders();
       return $this->sendRequest($requestType,$method,$header,null);

    }


    public function GetShippingMethods($countryCode=null)
    {
        $requestType='GET';
        $method='/api/Common/GetShippingMethods';
        $header=$this->buildHeaders();
        if ($countryCode!=null){
            $method=$method.'?countryCode='.$countryCode;
        }


        return $this->sendRequest($requestType,$method,$header,null);

    }
    private function sendRequest($requestType, $method, $header, $data){
        $url = $this->url . $method;
        $ch = curl_init();
        // Set the request type
        if ($requestType === 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $requestType);
        }
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }


}