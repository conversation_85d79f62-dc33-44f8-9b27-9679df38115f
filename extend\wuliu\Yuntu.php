<?php
/*
*Created By PhpStorm
*File:Yuntu.php
*User:尊杨科技
*Date:2025/6/3
*/

namespace wuliu;

class Yuntu
{

    protected $apiKey='';
    protected $secretKey='';
    protected $url='';
    public function __construct()
    {
        $this->apiKey="ITC0893791";
        $this->secretKey='axzc2utvPbfc9UbJDOh+7w==';
        $this->url='http://omsapi.uat.yunexpress.com';
    }

    private function buildHeaders(){
        return [
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Basic ' . base64_encode($this->apiKey.'&'.$this->secretKey),
            'charset: UTF-8'
        ];
    }

    /**
     * 创建订单
     * @param $params
     * @return bool|string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2025/6/3
     */
    public function createOrder($params)
    {
        // 根据云途物流API文档，构建完整的订单参数
        $order_param = [
            'CustomerOrderNumber' => $params['CustomerOrderNumber'], //客户订单号
            'ShippingMethodCode' => $params['ShippingMethodCode'], //运输方式代码
            'PackageCount' => intval($params['PackageCount']), //包裹数量
            'Weight' => round(floatval($params['Weight']), 3), //包裹重量,单位Kg,保留3位小数
            'Length' => 10, // 长度，默认值
            'Width' => 10,  // 宽度，默认值
            'Height' => 10, // 高度，默认值
            'Receiver' => [
                'CountryCode' => $params['Receiver']['CountryCode'], //国家简码
                'FirstName' => $params['Receiver']['FirstName'], //收件人名字
                'Street' => $params['Receiver']['Street'], //收件人详细地址
                'City' => $params['Receiver']['City'], //收件人城市
                'State' => $params['Receiver']['State'], //收件人省/州
                'Zip' => $params['Receiver']['Zip'], //收件人邮编
                'Phone' => $params['Receiver']['Phone'], //收件人电话
            ],
//            'Sender' => [
//                'CountryCode' => $params['Sender']['CountryCode'], //国家简码
//                'FirstName' => $params['Sender']['FirstName'], //发件人名字
//                'Street' => $params['Sender']['Street'], //发件人详细地址
//                'City' => $params['Sender']['City'], //发件人城市
//                'State' => $params['Sender']['State'], //发件人省/州
//                'Zip' => $params['Sender']['Zip'], //发件人邮编
//                'Phone' => $params['Sender']['Phone'], //发件人电话
//            ],
            // Parcels应该是一个数组，包含商品信息
            'Parcels' => [[
                'EName' => $params['Parcels']['EName'], //商品名称
                'CName' => $params['Parcels']['CName'], //商品名称
                'Quantity' => intval($params['Parcels']['Quantity']), //商品数量
                'UnitPrice' => floatval($params['Parcels']['UnitPrice']), //商品单价
                'UnitWeight' => floatval($params['Parcels']['UnitWeight']), //商品重量
                'CurrencyCode' => $params['Parcels']['CurrencyCode'] ?? "USD", //货币代码
            ]]
        ];
$params=[
    "CustomerOrderNumber"=>"9001113360sasas",
    "ShippingMethodCode"=>
];
        $requestType = 'POST';
        $method = '/api/WayBill/CreateOrder';
        $header = $this->buildHeaders();

        // 临时调试：输出请求参数
        error_log('云途物流请求参数: ' . json_encode($order_param, JSON_UNESCAPED_UNICODE));

        return $this->sendRequest($requestType, $method, $header, $order_param);
    }

    /**
     * 获取国家简码
     * @return bool|string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2025/6/3
     */
    public function getCountry()
    {
       $requestType='GET';
       $method='/api/Common/GetCountry';
       $header=$this->buildHeaders();
       return $this->sendRequest($requestType,$method,$header,null);

    }


    public function GetShippingMethods($countryCode=null)
    {
        $requestType='GET';
        $method='/api/Common/GetShippingMethods';
        $header=$this->buildHeaders();
        if ($countryCode!=null){
            $method=$method.'?countryCode='.$countryCode;
        }


        return $this->sendRequest($requestType,$method,$header,null);

    }
    private function sendRequest($requestType, $method, $header, $data){
        $url = $this->url . $method;
        $ch = curl_init();
        // Set the request type
        if ($requestType === 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $requestType);
        }
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }


}